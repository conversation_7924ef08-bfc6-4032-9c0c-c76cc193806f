using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.Linq;
using TMPro;

namespace Stylo.MenUI
{
    /// <summary>
    /// Manages the tabbed settings interface with category selection on the left
    /// and content panels on the right. Handles navigation between different settings categories.
    /// </summary>
    public class SettingsPanelManager : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private GameObject settingsPanel;
        [SerializeField] private Transform categoryButtonContainer;
        [SerializeField] private Transform contentContainer;
        [SerializeField] private Button backButton;
        [SerializeField] private Button applyButton;
        [SerializeField] private Button resetButton;

        [Header("Category Button Prefab")]
        [SerializeField] private GameObject categoryButtonPrefab;

        [Header("Settings Categories")]
        [SerializeField] private List<SettingsCategory> settingsCategories = new List<SettingsCategory>();

        [Header("Styling")]
        [SerializeField] private Color selectedCategoryColor = new Color(0f, 1f, 1f, 1f); // <PERSON>an
        [SerializeField] private Color normalCategoryColor = new Color(0.8f, 0.8f, 0.8f, 1f); // Light gray
        [SerializeField] private Color hoverCategoryColor = new Color(0.5f, 0.9f, 1f, 1f); // Light cyan

        // State
        private int _currentCategoryIndex = 0;
        private List<Button> _categoryButtons = new List<Button>();
        private MenUISystem _menUISystem;

        // Events
        public System.Action OnSettingsApplied;
        public System.Action OnSettingsReset;
        public System.Action<string> OnCategoryChanged;

        [System.Serializable]
        public class SettingsCategory
        {
            public string categoryName;
            public GameObject contentPanel;
            public Sprite categoryIcon;
            [TextArea(2, 4)]
            public string categoryDescription;
        }

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeComponents();
            SetupCategoryButtons();
            SetupButtonCallbacks();
        }

        private void Start()
        {
            // Hide settings panel initially
            if (settingsPanel != null)
                settingsPanel.SetActive(false);

            // Show first category by default
            if (settingsCategories.Count > 0)
                ShowCategory(0);
        }

        #endregion

        #region Initialization

        private void InitializeComponents()
        {
            // Find MenUISystem reference
            _menUISystem = GetComponentInParent<MenUISystem>();
            if (_menUISystem == null)
                _menUISystem = FindObjectOfType<MenUISystem>();

            // Auto-find components if not assigned
            if (settingsPanel == null)
                settingsPanel = transform.Find("SettingsPanel")?.gameObject;

            if (categoryButtonContainer == null)
                categoryButtonContainer = transform.Find("SettingsPanel/CategoryContainer");

            if (contentContainer == null)
                contentContainer = transform.Find("SettingsPanel/ContentContainer");

            if (backButton == null)
                backButton = transform.Find("SettingsPanel/BackButton")?.GetComponent<Button>();

            if (applyButton == null)
                applyButton = transform.Find("SettingsPanel/ApplyButton")?.GetComponent<Button>();

            if (resetButton == null)
                resetButton = transform.Find("SettingsPanel/ResetButton")?.GetComponent<Button>();
        }

        private void SetupCategoryButtons()
        {
            if (categoryButtonContainer == null || categoryButtonPrefab == null)
            {
                Debug.LogWarning("SettingsPanelManager: Category button container or prefab not assigned");
                return;
            }

            // Clear existing buttons
            foreach (Transform child in categoryButtonContainer)
            {
                if (Application.isPlaying)
                    Destroy(child.gameObject);
                else
                    DestroyImmediate(child.gameObject);
            }
            _categoryButtons.Clear();

            // Create buttons for each category
            for (int i = 0; i < settingsCategories.Count; i++)
            {
                CreateCategoryButton(i);
            }
        }

        private void CreateCategoryButton(int categoryIndex)
        {
            var category = settingsCategories[categoryIndex];
            
            // Instantiate button
            GameObject buttonObj = Instantiate(categoryButtonPrefab, categoryButtonContainer);
            Button button = buttonObj.GetComponent<Button>();
            
            if (button == null)
            {
                Debug.LogError($"SettingsPanelManager: Category button prefab must have a Button component");
                return;
            }

            // Setup button text
            TextMeshProUGUI buttonText = button.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
            {
                buttonText.text = category.categoryName;
            }

            // Setup button icon if available
            Image buttonIcon = button.transform.Find("Icon")?.GetComponent<Image>();
            if (buttonIcon != null && category.categoryIcon != null)
            {
                buttonIcon.sprite = category.categoryIcon;
            }

            // Setup button callback
            int index = categoryIndex; // Capture for closure
            button.onClick.AddListener(() => ShowCategory(index));

            // Add to list
            _categoryButtons.Add(button);

            // Apply initial styling
            UpdateCategoryButtonStyling(categoryIndex, categoryIndex == _currentCategoryIndex);
        }

        private void SetupButtonCallbacks()
        {
            if (backButton != null)
                backButton.onClick.AddListener(OnBackClicked);

            if (applyButton != null)
                applyButton.onClick.AddListener(OnApplyClicked);

            if (resetButton != null)
                resetButton.onClick.AddListener(OnResetClicked);
        }

        #endregion

        #region Public API

        /// <summary>
        /// Show the settings panel
        /// </summary>
        public void ShowSettings()
        {
            if (settingsPanel != null)
            {
                settingsPanel.SetActive(true);
                
                // Set focus to first category button for controller navigation
                if (_categoryButtons.Count > 0 && _categoryButtons[0] != null)
                    _categoryButtons[0].Select();
            }
        }

        /// <summary>
        /// Hide the settings panel
        /// </summary>
        public void HideSettings()
        {
            if (settingsPanel != null)
                settingsPanel.SetActive(false);
        }

        /// <summary>
        /// Show a specific settings category
        /// </summary>
        public void ShowCategory(int categoryIndex)
        {
            if (categoryIndex < 0 || categoryIndex >= settingsCategories.Count)
                return;

            // Hide all content panels
            foreach (var category in settingsCategories)
            {
                if (category.contentPanel != null)
                    category.contentPanel.SetActive(false);
            }

            // Show selected category panel
            var selectedCategory = settingsCategories[categoryIndex];
            if (selectedCategory.contentPanel != null)
                selectedCategory.contentPanel.SetActive(true);

            // Update button styling
            for (int i = 0; i < _categoryButtons.Count; i++)
            {
                UpdateCategoryButtonStyling(i, i == categoryIndex);
            }

            _currentCategoryIndex = categoryIndex;

            // Trigger event
            OnCategoryChanged?.Invoke(selectedCategory.categoryName);
        }

        /// <summary>
        /// Show category by name
        /// </summary>
        public void ShowCategory(string categoryName)
        {
            int index = settingsCategories.FindIndex(c => c.categoryName.Equals(categoryName, System.StringComparison.OrdinalIgnoreCase));
            if (index >= 0)
                ShowCategory(index);
        }

        /// <summary>
        /// Get current category name
        /// </summary>
        public string GetCurrentCategoryName()
        {
            if (_currentCategoryIndex >= 0 && _currentCategoryIndex < settingsCategories.Count)
                return settingsCategories[_currentCategoryIndex].categoryName;
            return "";
        }

        #endregion

        #region Button Callbacks

        private void OnBackClicked()
        {
            HideSettings();
            
            // Return to main pause menu
            if (_menUISystem != null)
            {
                // The MenUISystem should handle showing the main pause menu
                // This will be implemented when we integrate with MenUISystem
            }
        }

        private void OnApplyClicked()
        {
            // Apply all settings changes
            OnSettingsApplied?.Invoke();
        }

        private void OnResetClicked()
        {
            // Reset current category settings to defaults
            OnSettingsReset?.Invoke();
        }

        #endregion

        #region Styling

        private void UpdateCategoryButtonStyling(int buttonIndex, bool isSelected)
        {
            if (buttonIndex < 0 || buttonIndex >= _categoryButtons.Count)
                return;

            Button button = _categoryButtons[buttonIndex];
            if (button == null) return;

            // Update button colors
            ColorBlock colors = button.colors;
            colors.normalColor = isSelected ? selectedCategoryColor : normalCategoryColor;
            colors.highlightedColor = hoverCategoryColor;
            colors.selectedColor = selectedCategoryColor;
            button.colors = colors;

            // Update text color if needed
            TextMeshProUGUI buttonText = button.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
            {
                buttonText.color = isSelected ? Color.white : Color.gray;
            }
        }

        #endregion

        #region Editor Helpers

#if UNITY_EDITOR
        [ContextMenu("Auto-Find Settings Categories")]
        public void EditorAutoFindCategories()
        {
            settingsCategories.Clear();
            
            if (contentContainer == null) return;

            // Find all child panels that could be settings categories
            foreach (Transform child in contentContainer)
            {
                var category = new SettingsCategory
                {
                    categoryName = child.name.Replace("Panel", "").Replace("Settings", ""),
                    contentPanel = child.gameObject
                };
                settingsCategories.Add(category);
            }

            Debug.Log($"SettingsPanelManager: Found {settingsCategories.Count} potential settings categories");
        }

        [ContextMenu("Refresh Category Buttons")]
        public void EditorRefreshButtons()
        {
            if (Application.isPlaying)
                SetupCategoryButtons();
        }
#endif

        #endregion
    }
}
