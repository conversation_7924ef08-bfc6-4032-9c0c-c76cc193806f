using UnityEngine;
using BTR;

namespace Stylo.MenUI
{
    /// <summary>
    /// Validation script to test MenUI system functionality.
    /// Can be temporarily added to a GameObject for testing purposes.
    /// </summary>
    public class MenUIValidator : MonoBehaviour
    {
        [Header("Test Configuration")]
        [SerializeField] private bool runValidationOnStart = true;
        [SerializeField] private bool enableDetailedLogs = true;

        [Header("Test Controls")]
        [SerializeField] private KeyCode testShowPauseKey = KeyCode.P;
        [SerializeField] private KeyCode testHidePauseKey = KeyCode.O;
        [SerializeField] private KeyCode testTogglePauseKey = KeyCode.I;

        private MenUISystem menUISystem;
        private GameManager gameManager;

        private void Start()
        {
            if (runValidationOnStart)
            {
                ValidateSystem();
            }
        }

        private void Update()
        {
            // Test controls for manual testing
            if (Input.GetKeyDown(testShowPauseKey))
            {
                TestShowPause();
            }
            else if (Input.GetKeyDown(testHidePauseKey))
            {
                TestHidePause();
            }
            else if (Input.GetKeyDown(testTogglePauseKey))
            {
                TestTogglePause();
            }
        }

        /// <summary>
        /// Validates the MenUI system setup and integration
        /// </summary>
        public void ValidateSystem()
        {
            Debug.Log("=== MenUI System Validation Started ===");

            // Find MenUISystem
            menUISystem = FindObjectOfType<MenUISystem>();
            if (menUISystem == null)
            {
                Debug.LogError("❌ MenUISystem not found in scene! Create one using Stylo → MenUI → Create Pause Menu UI");
                return;
            }
            else
            {
                Debug.Log("✅ MenUISystem found in scene");
            }

            // Find GameManager
            gameManager = GameManager.Instance;
            if (gameManager == null)
            {
                Debug.LogWarning("⚠️ GameManager not found in scene. This is normal if testing in isolation.");
            }
            else
            {
                Debug.Log("✅ GameManager found in scene");

                // Check if GameManager has MenUISystem assigned
                var pauseMenuManagerField = typeof(GameManager).GetField("pauseMenuManager",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (pauseMenuManagerField != null)
                {
                    var assignedMenUI = pauseMenuManagerField.GetValue(gameManager) as MenUISystem;
                    if (assignedMenUI == null)
                    {
                        Debug.LogWarning("⚠️ GameManager's pauseMenuManager field is not assigned. Assign the MenUISystem in the inspector.");
                    }
                    else if (assignedMenUI == menUISystem)
                    {
                        Debug.Log("✅ GameManager correctly references the MenUISystem");
                    }
                    else
                    {
                        Debug.LogWarning("⚠️ GameManager references a different MenUISystem instance");
                    }
                }
            }

            // Check TimeManager
            if (TimeManager.Instance == null)
            {
                Debug.LogError("❌ TimeManager.Instance not found! MenUI pause/resume functionality will not work properly.");
            }
            else
            {
                Debug.Log("✅ TimeManager.Instance found - pause/resume will work correctly");
            }

            // Check Input System
            try
            {
                var controls = new DefaultControls();
                var pauseAction = controls.UI.Pause;
                if (pauseAction != null)
                {
                    Debug.Log("✅ Input system Pause action found and accessible");
                }
                controls.Dispose();
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ Input system validation failed: {ex.Message}");
            }

            // Check UI Interaction Components
            ValidateUIInteractionComponents();

            // Check UI Components
            ValidateUIComponents();

            Debug.Log("=== MenUI System Validation Complete ===");

            if (enableDetailedLogs)
            {
                Debug.Log($"Test Controls: Show Pause ({testShowPauseKey}), Hide Pause ({testHidePauseKey}), Toggle Pause ({testTogglePauseKey})");
            }
        }

        private void ValidateUIInteractionComponents()
        {
            Debug.Log("--- UI INTERACTION VALIDATION ---");

            // Check EventSystem
            var eventSystem = UnityEngine.EventSystems.EventSystem.current;
            if (eventSystem == null)
            {
                Debug.LogError("❌ No EventSystem found! Mouse/touch interaction will not work.");
                Debug.LogWarning("   → Create an EventSystem GameObject with EventSystem and InputSystemUIInputModule components");
            }
            else
            {
                Debug.Log("✅ EventSystem found");

                // Check Input Module
                var inputModule = eventSystem.GetComponent<UnityEngine.InputSystem.UI.InputSystemUIInputModule>();
                var legacyInputModule = eventSystem.GetComponent<UnityEngine.EventSystems.StandaloneInputModule>();

                if (inputModule != null)
                {
                    Debug.Log("✅ InputSystemUIInputModule found (New Input System)");
                }
                else if (legacyInputModule != null)
                {
                    Debug.Log("✅ StandaloneInputModule found (Legacy Input System)");
                }
                else
                {
                    Debug.LogWarning("⚠️ No Input Module found on EventSystem! UI interaction may not work properly.");
                }
            }

            // Check Canvas and GraphicRaycaster
            var canvas = menUISystem?.GetComponentInParent<Canvas>();
            if (canvas == null)
            {
                Debug.LogError("❌ No Canvas found! MenUISystem must be on or under a Canvas.");
            }
            else
            {
                Debug.Log("✅ Canvas found");

                var graphicRaycaster = canvas.GetComponent<UnityEngine.UI.GraphicRaycaster>();
                if (graphicRaycaster == null)
                {
                    Debug.LogError("❌ No GraphicRaycaster found on Canvas! Mouse interaction will not work.");
                    Debug.LogWarning("   → Add a GraphicRaycaster component to the Canvas");
                }
                else
                {
                    Debug.Log("✅ GraphicRaycaster found on Canvas");
                }

                // Check Canvas settings
                if (canvas.renderMode != RenderMode.ScreenSpaceOverlay)
                {
                    Debug.LogWarning($"⚠️ Canvas render mode is {canvas.renderMode}. Screen Space - Overlay is recommended for pause menus.");
                }
                else
                {
                    Debug.Log("✅ Canvas render mode is Screen Space - Overlay");
                }
            }
        }

        private void ValidateUIComponents()
        {
            if (menUISystem == null) return;

            Debug.Log("--- UI COMPONENTS VALIDATION ---");

            // Use reflection to check private fields
            var pauseMenuPanelField = typeof(MenUISystem).GetField("pauseMenuPanel",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var overlayPanelField = typeof(MenUISystem).GetField("overlayPanel",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var enableOverlayField = typeof(MenUISystem).GetField("enableOverlay",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var overlayColorField = typeof(MenUISystem).GetField("overlayColor",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var resumeButtonField = typeof(MenUISystem).GetField("resumeButton",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var settingsButtonField = typeof(MenUISystem).GetField("settingsButton",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var exitButtonField = typeof(MenUISystem).GetField("exitButton",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            var pauseMenuPanel = pauseMenuPanelField?.GetValue(menUISystem) as GameObject;
            var overlayPanel = overlayPanelField?.GetValue(menUISystem) as GameObject;
            var enableOverlay = (bool)(enableOverlayField?.GetValue(menUISystem) ?? false);
            var overlayColor = (Color)(overlayColorField?.GetValue(menUISystem) ?? Color.black);
            var resumeButton = resumeButtonField?.GetValue(menUISystem);
            var settingsButton = settingsButtonField?.GetValue(menUISystem);
            var exitButton = exitButtonField?.GetValue(menUISystem);

            // Validate Overlay Panel
            Debug.Log("--- OVERLAY VALIDATION ---");
            Debug.Log($"Enable Overlay: {enableOverlay}");
            Debug.Log($"Overlay Color: {overlayColor}");

            if (overlayPanel == null)
            {
                Debug.LogWarning("⚠️ OverlayPanel not assigned in MenUISystem! This is why you don't see the overlay.");
                Debug.LogWarning("   → Try using the 'Recreate Overlay Panel' context menu option on MenUISystem");
            }
            else
            {
                Debug.Log("✅ OverlayPanel assigned");

                // Check overlay panel configuration
                var overlayImage = overlayPanel.GetComponent<UnityEngine.UI.Image>();
                if (overlayImage == null)
                {
                    Debug.LogError("❌ OverlayPanel has no Image component! Cannot display overlay.");
                }
                else
                {
                    Debug.Log($"✅ OverlayPanel Image component found with color: {overlayImage.color}");
                }

                // Check overlay panel position in hierarchy
                Debug.Log($"Overlay Panel active: {overlayPanel.activeInHierarchy}");
                Debug.Log($"Overlay Panel sibling index: {overlayPanel.transform.GetSiblingIndex()}");

                // Check RectTransform
                var overlayRect = overlayPanel.GetComponent<RectTransform>();
                if (overlayRect != null)
                {
                    Debug.Log($"Overlay RectTransform - AnchorMin: {overlayRect.anchorMin}, AnchorMax: {overlayRect.anchorMax}");
                    Debug.Log($"Overlay RectTransform - SizeDelta: {overlayRect.sizeDelta}, Position: {overlayRect.anchoredPosition}");
                }
            }

            // Validate Pause Menu Panel
            Debug.Log("--- PAUSE MENU VALIDATION ---");
            if (pauseMenuPanel == null)
                Debug.LogWarning("⚠️ PauseMenuPanel not assigned in MenUISystem");
            else
            {
                Debug.Log("✅ PauseMenuPanel assigned");

                // Check if panel is properly deactivated by default
                if (pauseMenuPanel.activeInHierarchy)
                {
                    Debug.LogWarning("⚠️ PauseMenuPanel is currently active! It should be deactivated by default.");
                }
                else
                {
                    Debug.Log("✅ PauseMenuPanel is properly deactivated by default");
                }

                // Check hierarchy order
                if (overlayPanel != null)
                {
                    int overlayIndex = overlayPanel.transform.GetSiblingIndex();
                    int menuIndex = pauseMenuPanel.transform.GetSiblingIndex();
                    if (overlayIndex < menuIndex)
                    {
                        Debug.Log("✅ Overlay is behind pause menu (correct order)");
                    }
                    else
                    {
                        Debug.LogWarning("⚠️ Overlay is in front of pause menu! This will block button clicks.");
                    }
                }

                ValidateTextMeshProComponents(pauseMenuPanel);
            }

            // Validate Buttons
            Debug.Log("--- BUTTON VALIDATION ---");
            if (resumeButton == null)
                Debug.LogWarning("⚠️ Resume button not assigned in MenUISystem");
            else
                Debug.Log("✅ Resume button assigned");

            if (settingsButton == null)
                Debug.LogWarning("⚠️ Settings button not assigned in MenUISystem");
            else
                Debug.Log("✅ Settings button assigned");

            if (exitButton == null)
                Debug.LogWarning("⚠️ Exit button not assigned in MenUISystem");
            else
                Debug.Log("✅ Exit button assigned");
        }

        private void ValidateTextMeshProComponents(GameObject pauseMenuPanel)
        {
            // Find all TextMeshPro components in the pause menu
            var tmpComponents = pauseMenuPanel.GetComponentsInChildren<TMPro.TextMeshProUGUI>();

            if (tmpComponents.Length == 0)
            {
                Debug.LogWarning("⚠️ No TextMeshPro components found in pause menu! Text may not render properly.");
                return;
            }

            Debug.Log($"✅ Found {tmpComponents.Length} TextMeshPro components");

            foreach (var tmp in tmpComponents)
            {
                if (tmp.font == null)
                {
                    Debug.LogWarning($"⚠️ TextMeshPro component '{tmp.name}' has no font assigned!");
                }
                else
                {
                    Debug.Log($"✅ TextMeshPro '{tmp.name}' using font: {tmp.font.name}");

                    // Check if it's using a cyberpunk font
                    string fontName = tmp.font.name.ToLower();
                    if (fontName.Contains("wo3") || fontName.Contains("fusion") || fontName.Contains("square"))
                    {
                        Debug.Log($"🎯 Cyberpunk font detected: {tmp.font.name}");
                    }
                }

                // Check text content
                if (string.IsNullOrEmpty(tmp.text))
                {
                    Debug.LogWarning($"⚠️ TextMeshPro component '{tmp.name}' has no text content!");
                }
                else
                {
                    Debug.Log($"✅ TextMeshPro '{tmp.name}' text: '{tmp.text}'");
                }
            }
        }

        public void TestShowPause()
        {
            if (menUISystem != null)
            {
                Debug.Log("Testing ShowPause()...");
                menUISystem.ShowPause();
            }
        }

        public void TestHidePause()
        {
            if (menUISystem != null)
            {
                Debug.Log("Testing HidePause()...");
                menUISystem.HidePause();
            }
        }

        public void TestTogglePause()
        {
            if (menUISystem != null)
            {
                Debug.Log("Testing TogglePause()...");
                menUISystem.TogglePause();
            }
        }

        /// <summary>
        /// Diagnose overlay panel issues specifically
        /// </summary>
        [ContextMenu("Diagnose Overlay Issues")]
        public void DiagnoseOverlayIssues()
        {
            if (menUISystem == null)
            {
                Debug.LogError("No MenUISystem found!");
                return;
            }

            Debug.Log("=== OVERLAY DIAGNOSTIC STARTED ===");

            // Get overlay panel via reflection
            var overlayPanelField = typeof(MenUISystem).GetField("overlayPanel",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var enableOverlayField = typeof(MenUISystem).GetField("enableOverlay",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var overlayColorField = typeof(MenUISystem).GetField("overlayColor",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            var overlayPanel = overlayPanelField?.GetValue(menUISystem) as GameObject;
            var enableOverlay = (bool)(enableOverlayField?.GetValue(menUISystem) ?? false);
            var overlayColor = (Color)(overlayColorField?.GetValue(menUISystem) ?? Color.black);

            Debug.Log($"Enable Overlay Setting: {enableOverlay}");
            Debug.Log($"Overlay Color: {overlayColor}");

            if (overlayPanel == null)
            {
                Debug.LogError("❌ PROBLEM FOUND: OverlayPanel is NULL!");
                Debug.LogWarning("SOLUTION: Right-click on MenUISystem component → 'Recreate Overlay Panel'");
                return;
            }

            Debug.Log($"✅ OverlayPanel exists: {overlayPanel.name}");

            // Check Image component
            var image = overlayPanel.GetComponent<UnityEngine.UI.Image>();
            if (image == null)
            {
                Debug.LogError("❌ PROBLEM FOUND: OverlayPanel has no Image component!");
                Debug.LogWarning("SOLUTION: Right-click on MenUISystem component → 'Recreate Overlay Panel'");
                return;
            }

            Debug.Log($"✅ Image component found with color: {image.color}");

            // Check if color has transparency
            if (image.color.a <= 0.01f)
            {
                Debug.LogWarning("⚠️ POTENTIAL ISSUE: Overlay alpha is very low, might be invisible!");
                Debug.LogWarning($"Current alpha: {image.color.a}. Try setting it to 0.5-0.8 for visibility.");
            }

            // Check RectTransform
            var rectTransform = overlayPanel.GetComponent<RectTransform>();
            if (rectTransform == null)
            {
                Debug.LogError("❌ PROBLEM FOUND: OverlayPanel has no RectTransform!");
                return;
            }

            Debug.Log($"✅ RectTransform found");
            Debug.Log($"   AnchorMin: {rectTransform.anchorMin}, AnchorMax: {rectTransform.anchorMax}");
            Debug.Log($"   SizeDelta: {rectTransform.sizeDelta}, Position: {rectTransform.anchoredPosition}");

            // Check if it's set to full screen
            bool isFullScreen = rectTransform.anchorMin == Vector2.zero && rectTransform.anchorMax == Vector2.one;
            if (!isFullScreen)
            {
                Debug.LogWarning("⚠️ POTENTIAL ISSUE: Overlay is not set to full screen!");
                Debug.LogWarning("SOLUTION: Set AnchorMin to (0,0) and AnchorMax to (1,1)");
            }

            // Check Canvas
            var canvas = menUISystem.GetComponentInParent<Canvas>();
            if (canvas == null)
            {
                Debug.LogError("❌ PROBLEM FOUND: No Canvas found!");
                return;
            }

            Debug.Log($"✅ Canvas found: {canvas.name}");
            Debug.Log($"   Render Mode: {canvas.renderMode}");
            Debug.Log($"   Sort Order: {canvas.sortingOrder}");

            // Check hierarchy order
            var pauseMenuPanelField = typeof(MenUISystem).GetField("pauseMenuPanel",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var pauseMenuPanel = pauseMenuPanelField?.GetValue(menUISystem) as GameObject;

            if (pauseMenuPanel != null)
            {
                int overlayIndex = overlayPanel.transform.GetSiblingIndex();
                int menuIndex = pauseMenuPanel.transform.GetSiblingIndex();
                Debug.Log($"Overlay sibling index: {overlayIndex}, Menu sibling index: {menuIndex}");

                if (overlayIndex >= menuIndex)
                {
                    Debug.LogWarning("⚠️ POTENTIAL ISSUE: Overlay is in front of or same level as pause menu!");
                    Debug.LogWarning("SOLUTION: Overlay should be behind the pause menu in hierarchy");
                }
            }

            // Test visibility
            Debug.Log($"Current overlay active state: {overlayPanel.activeInHierarchy}");

            Debug.Log("=== OVERLAY DIAGNOSTIC COMPLETE ===");
            Debug.Log("If overlay is still not visible, try:");
            Debug.Log("1. Right-click MenUISystem → 'Recreate Overlay Panel'");
            Debug.Log("2. Increase overlay alpha value");
            Debug.Log("3. Check Canvas render mode and sort order");
        }

        /// <summary>
        /// Quick setup method to create a basic MenUI system for testing
        /// </summary>
        [ContextMenu("Quick Setup MenUI for Testing")]
        public void QuickSetupForTesting()
        {
            Canvas canvas = FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                // Create a basic canvas
                GameObject canvasGO = new GameObject("Test Canvas");
                canvas = canvasGO.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvas.sortingOrder = 100;

                // Add GraphicRaycaster for UI interaction
                canvasGO.AddComponent<UnityEngine.UI.GraphicRaycaster>();

                Debug.Log("Created test Canvas");
            }

            if (FindObjectOfType<MenUISystem>() == null)
            {
                MenUISetupHelper.CreatePauseMenuUI(canvas);
                Debug.Log("Created MenUI system for testing");
            }
            else
            {
                Debug.Log("MenUISystem already exists in scene");
            }
        }
    }

#if UNITY_EDITOR
    /// <summary>
    /// Editor utility for MenUI validation
    /// </summary>
    public class MenUIValidatorUtility
    {
        [UnityEditor.MenuItem("Stylo/MenUI/Validate System")]
        public static void ValidateSystem()
        {
            var validator = Object.FindObjectOfType<MenUIValidator>();
            if (validator == null)
            {
                GameObject validatorGO = new GameObject("MenUI Validator (Temporary)");
                validator = validatorGO.AddComponent<MenUIValidator>();
            }

            validator.ValidateSystem();
        }

        [UnityEditor.MenuItem("Stylo/MenUI/Quick Test Setup")]
        public static void QuickTestSetup()
        {
            var validator = Object.FindObjectOfType<MenUIValidator>();
            if (validator == null)
            {
                GameObject validatorGO = new GameObject("MenUI Validator (Temporary)");
                validator = validatorGO.AddComponent<MenUIValidator>();
            }

            validator.QuickSetupForTesting();
            validator.ValidateSystem();
        }
    }
#endif
}
