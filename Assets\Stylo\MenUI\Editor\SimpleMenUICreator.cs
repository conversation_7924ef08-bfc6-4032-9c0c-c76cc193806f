using UnityEngine;
using UnityEngine.UI;
using UnityEditor;
using TMPro;

namespace Stylo.MenUI.Editor
{
    /// <summary>
    /// Simple, streamlined editor script to create a working MenUI system.
    /// No over-engineering - just creates what's needed and assigns everything properly.
    /// </summary>
    public static class SimpleMenUICreator
    {
        private static void SetupTextMeshProFont(TextMeshProUGUI textComponent)
        {
            // Try multiple font sources to ensure text displays properly
            try
            {
                // First try: Load LiberationSans SDF from multiple possible locations
                TMP_FontAsset liberationFont = null;

                // Try standard TextMeshPro location
                liberationFont = Resources.Load<TMP_FontAsset>("Fonts & Materials/LiberationSans SDF");
                if (liberationFont != null)
                {
                    textComponent.font = liberationFont;
                    Debug.Log("MenUI: Using LiberationSans SDF from standard location");
                    return;
                }

                // Try SharedAssets location
                liberationFont = Resources.Load<TMP_FontAsset>("FontsAndMaterials/LiberationSansSDF");
                if (liberationFont != null)
                {
                    textComponent.font = liberationFont;
                    Debug.Log("MenUI: Using LiberationSans SDF from SharedAssets location");
                    return;
                }

                // Second try: TMP default font asset
                if (TMPro.TMP_Settings.defaultFontAsset != null)
                {
                    textComponent.font = TMPro.TMP_Settings.defaultFontAsset;
                    Debug.Log("MenUI: Using TMP default font asset");
                    return;
                }

                // Third try: Find any TMP font in the project
                var tmpFonts = Resources.FindObjectsOfTypeAll<TMPro.TMP_FontAsset>();
                if (tmpFonts.Length > 0)
                {
                    textComponent.font = tmpFonts[0];
                    Debug.Log($"MenUI: Using found TMP font: {tmpFonts[0].name}");
                    return;
                }

                Debug.LogError("MenUI: No TextMeshPro fonts found! Please import TMP Essential Resources via Window > TextMeshPro > Import TMP Essential Resources");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"MenUI Font setup failed: {ex.Message}. Please import TMP Essential Resources via Window > TextMeshPro > Import TMP Essential Resources");
            }
        }

        [MenuItem("Stylo/MenUI/Create Simple MenUI System")]
        public static void CreateSimpleMenUISystem()
        {
            // Check if there's already a MenUI system and ask user if they want to replace it
            SimpleMenUISystem existingSystem = Object.FindObjectOfType<SimpleMenUISystem>();
            if (existingSystem != null)
            {
                bool replace = EditorUtility.DisplayDialog(
                    "MenUI System Already Exists",
                    "A MenUI system already exists in the scene. Do you want to replace it with a new one?",
                    "Replace", "Cancel");

                if (!replace) return;

                // Remove existing system
                Object.DestroyImmediate(existingSystem.gameObject);
            }

            // Always create a dedicated Canvas for MenUI to avoid conflicts
            GameObject canvasGO = new GameObject("MenUI Canvas");
            Canvas canvas = canvasGO.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvas.sortingOrder = 1000; // Very high sort order to ensure it's on top
            canvasGO.AddComponent<GraphicRaycaster>();

            // Add CanvasScaler with proper settings for consistent UI scaling
            CanvasScaler scaler = canvasGO.AddComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            scaler.referenceResolution = new Vector2(1920, 1080);
            scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
            scaler.matchWidthOrHeight = 0.5f;

            Debug.Log("Created dedicated MenUI Canvas to avoid layout conflicts with existing UI systems");

            // Create main MenUI system
            GameObject menUIRoot = new GameObject("Simple MenUI System");
            menUIRoot.transform.SetParent(canvas.transform, false);
            SimpleMenUISystem menUISystem = menUIRoot.AddComponent<SimpleMenUISystem>();

            // Create overlay panel
            GameObject overlay = CreateOverlay(menUIRoot.transform);

            // Create pause menu panel
            GameObject pauseMenu = CreatePauseMenu(menUIRoot.transform);

            // Create settings panel
            GameObject settingsPanel = CreateSettingsPanel(menUIRoot.transform);

            // Assign all references
            AssignReferences(menUISystem, overlay, pauseMenu, settingsPanel);

            // Select the created system
            Selection.activeGameObject = menUIRoot;

            Debug.Log("Simple MenUI System created successfully! All references assigned and ready to use.");
            Debug.Log("✓ Improved font handling with multiple fallback options");
            Debug.Log("✓ Larger UI elements for better visibility");
            Debug.Log("✓ Better color scheme for improved contrast");
            Debug.Log("✓ Proper Canvas scaling for consistent appearance across resolutions");
        }



        private static GameObject CreateOverlay(Transform parent)
        {
            GameObject overlay = new GameObject("Overlay Panel");
            overlay.transform.SetParent(parent, false);

            // Add RectTransform first
            RectTransform rect = overlay.AddComponent<RectTransform>();
            rect.anchorMin = Vector2.zero;
            rect.anchorMax = Vector2.one;
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;

            // Add Image component for background
            Image image = overlay.AddComponent<Image>();
            image.color = new Color(0f, 0f, 0.05f, 0.8f); // Semi-transparent dark blue for better contrast

            return overlay;
        }

        private static GameObject CreatePauseMenu(Transform parent)
        {
            GameObject pauseMenu = new GameObject("Pause Menu Panel");
            pauseMenu.transform.SetParent(parent, false);

            // Add RectTransform first - properly centered on screen
            RectTransform rect = pauseMenu.AddComponent<RectTransform>();
            rect.anchorMin = new Vector2(0.3f, 0.25f); // Better centered positioning
            rect.anchorMax = new Vector2(0.7f, 0.75f);
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;

            // Add Image component with better dark theme
            Image image = pauseMenu.AddComponent<Image>();
            image.color = new Color(0.05f, 0.05f, 0.08f, 0.98f); // Very dark with high opacity

            // Create vertical layout with improved settings
            VerticalLayoutGroup layout = pauseMenu.AddComponent<VerticalLayoutGroup>();
            layout.spacing = 40; // Better spacing
            layout.padding = new RectOffset(80, 80, 80, 80); // Better padding
            layout.childAlignment = TextAnchor.MiddleCenter;
            layout.childControlHeight = false; // Don't control height - let buttons set their own
            layout.childControlWidth = true;   // Control width to make buttons same width
            layout.childForceExpandWidth = true;
            layout.childForceExpandHeight = false; // Don't force expand height

            // Create title
            CreatePauseMenuText("PAUSED", pauseMenu.transform, 72);

            // Add spacing
            CreateSpacer(pauseMenu.transform, 40);

            // Create buttons with proper sizing
            CreatePauseMenuButton("Resume", pauseMenu.transform, new Color(0.1f, 0.7f, 0.2f, 1f));
            CreatePauseMenuButton("Settings", pauseMenu.transform, new Color(0.2f, 0.5f, 0.9f, 1f));
            CreatePauseMenuButton("Exit", pauseMenu.transform, new Color(0.9f, 0.2f, 0.2f, 1f));

            return pauseMenu;
        }

        private static GameObject CreateSettingsPanel(Transform parent)
        {
            GameObject settingsPanel = new GameObject("Settings Panel");
            settingsPanel.transform.SetParent(parent, false);

            // Add RectTransform component first - properly centered, not full screen
            RectTransform rect = settingsPanel.AddComponent<RectTransform>();
            rect.anchorMin = new Vector2(0.15f, 0.1f);
            rect.anchorMax = new Vector2(0.85f, 0.9f);
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;

            // Add Image component with proper dark theme
            Image image = settingsPanel.AddComponent<Image>();
            image.color = new Color(0.05f, 0.05f, 0.1f, 0.98f); // Very dark blue

            // Create horizontal layout with improved settings
            HorizontalLayoutGroup layout = settingsPanel.AddComponent<HorizontalLayoutGroup>();
            layout.spacing = 20; // Better gap between tabs and content
            layout.padding = new RectOffset(50, 50, 50, 50); // Better padding around entire settings panel
            layout.childControlHeight = true;
            layout.childControlWidth = false;
            layout.childForceExpandHeight = true;
            layout.childForceExpandWidth = false;

            // Create category tabs (left side)
            GameObject categoryTabs = CreateCategoryTabs(settingsPanel.transform);

            // Create content area (right side)
            GameObject contentArea = CreateContentArea(settingsPanel.transform);

            return settingsPanel;
        }

        private static GameObject CreateCategoryTabs(Transform parent)
        {
            GameObject categoryTabs = new GameObject("Category Tabs");
            categoryTabs.transform.SetParent(parent, false);

            // Add RectTransform component with fixed width
            RectTransform rect = categoryTabs.AddComponent<RectTransform>();

            // Add LayoutElement to control sizing in horizontal layout
            LayoutElement layoutElement = categoryTabs.AddComponent<LayoutElement>();
            layoutElement.minWidth = 300;
            layoutElement.preferredWidth = 300;
            layoutElement.flexibleWidth = 0; // Don't expand

            // Add background with better styling
            Image background = categoryTabs.AddComponent<Image>();
            background.color = new Color(0.03f, 0.03f, 0.06f, 0.98f); // Very dark background

            // Add vertical layout for buttons
            VerticalLayoutGroup layout = categoryTabs.AddComponent<VerticalLayoutGroup>();
            layout.spacing = 15; // Spacing between buttons
            layout.padding = new RectOffset(20, 20, 20, 20); // Padding inside tabs area
            layout.childControlHeight = false;
            layout.childControlWidth = true;
            layout.childForceExpandWidth = true;
            layout.childForceExpandHeight = false;

            // Create category buttons with better colors and proper sizing
            CreateMediumButton("Graphics", categoryTabs.transform, new Color(0.2f, 0.5f, 0.9f, 1f)); // Brighter blue
            CreateMediumButton("Audio", categoryTabs.transform, new Color(0.6f, 0.2f, 0.9f, 1f)); // Brighter purple
            CreateMediumButton("Controls", categoryTabs.transform, new Color(0.9f, 0.5f, 0.2f, 1f)); // Brighter orange
            CreateMediumButton("Gameplay", categoryTabs.transform, new Color(0.2f, 0.9f, 0.4f, 1f)); // Brighter green
            CreateMediumButton("Debug", categoryTabs.transform, new Color(0.9f, 0.2f, 0.5f, 1f)); // Brighter pink

            // Add spacer to push back button to bottom
            GameObject spacer = new GameObject("Spacer");
            spacer.transform.SetParent(categoryTabs.transform, false);
            RectTransform spacerRect = spacer.AddComponent<RectTransform>();
            LayoutElement spacerLayout = spacer.AddComponent<LayoutElement>();
            spacerLayout.flexibleHeight = 1; // Takes up remaining space

            // Back button with distinct color
            CreateMediumButton("Back", categoryTabs.transform, new Color(0.6f, 0.6f, 0.6f, 1f)); // Lighter gray

            return categoryTabs;
        }

        private static GameObject CreateContentArea(Transform parent)
        {
            GameObject contentArea = new GameObject("Content Area");
            contentArea.transform.SetParent(parent, false);

            // Add RectTransform component
            RectTransform rect = contentArea.AddComponent<RectTransform>();

            // Add background
            Image background = contentArea.AddComponent<Image>();
            background.color = new Color(0.12f, 0.12f, 0.18f, 0.95f); // Slightly lighter content area

            // Add layout element to take remaining space properly
            LayoutElement layout = contentArea.AddComponent<LayoutElement>();
            layout.flexibleWidth = 1; // Take up remaining horizontal space
            layout.minWidth = 500; // Minimum width for content
            layout.preferredWidth = -1; // Let it expand

            // Create settings panels for each category (all positioned to fill the content area)
            CreateSettingsCategoryPanel("Graphics Settings Panel", contentArea.transform);
            CreateSettingsCategoryPanel("Audio Settings Panel", contentArea.transform);
            CreateSettingsCategoryPanel("Controls Settings Panel", contentArea.transform);
            CreateSettingsCategoryPanel("Gameplay Settings Panel", contentArea.transform);
            CreateSettingsCategoryPanel("Debug Settings Panel", contentArea.transform);

            return contentArea;
        }

        private static GameObject CreateSettingsCategoryPanel(string name, Transform parent)
        {
            GameObject panel = new GameObject(name);
            panel.transform.SetParent(parent, false);

            // Add RectTransform component first
            RectTransform rect = panel.AddComponent<RectTransform>();
            rect.anchorMin = Vector2.zero;
            rect.anchorMax = Vector2.one;
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;

            // Add Image component with subtle background
            Image image = panel.AddComponent<Image>();
            image.color = new Color(0.15f, 0.15f, 0.22f, 0.8f); // Subtle background

            // Add improved padding layout
            VerticalLayoutGroup layout = panel.AddComponent<VerticalLayoutGroup>();
            layout.padding = new RectOffset(30, 30, 30, 30);
            layout.spacing = 20;
            layout.childAlignment = TextAnchor.UpperCenter;
            layout.childControlHeight = false;
            layout.childControlWidth = true;
            layout.childForceExpandWidth = true;

            // Add title with proper styling - much larger
            string categoryName = name.Replace(" Settings Panel", "");
            CreateText($"{categoryName} Settings", panel.transform, 56, new Color(1f, 1f, 1f, 1f)); // Larger title, pure white

            // Add placeholder text for content - larger and more visible
            CreateText($"Add {categoryName.ToLower()} settings here...", panel.transform, 32, new Color(0.9f, 0.9f, 1f, 1f)); // Larger placeholder text

            return panel;
        }

        // Medium button for settings categories
        private static GameObject CreateMediumButton(string text, Transform parent, Color buttonColor)
        {
            GameObject buttonGO = new GameObject(text);
            buttonGO.transform.SetParent(parent, false);

            // Add RectTransform component first
            RectTransform rect = buttonGO.AddComponent<RectTransform>();

            // Add LayoutElement to control sizing in vertical layout
            LayoutElement layoutElement = buttonGO.AddComponent<LayoutElement>();
            layoutElement.minHeight = 70;
            layoutElement.preferredHeight = 70;
            layoutElement.flexibleHeight = 0; // Don't expand vertically

            // Add Image component with proper color
            Image image = buttonGO.AddComponent<Image>();
            image.color = buttonColor;

            // Add Button component with better color transitions
            Button button = buttonGO.AddComponent<Button>();
            ColorBlock colors = button.colors;
            colors.normalColor = Color.white;
            colors.highlightedColor = new Color(1.2f, 1.2f, 1.2f, 1f); // Brighter highlight
            colors.pressedColor = new Color(0.8f, 0.8f, 0.8f, 1f);
            colors.selectedColor = new Color(1.15f, 1.15f, 1.15f, 1f);
            colors.disabledColor = new Color(0.5f, 0.5f, 0.5f, 1f);
            button.colors = colors;

            // Create text child
            GameObject textGO = new GameObject("Text");
            textGO.transform.SetParent(buttonGO.transform, false);

            // Add RectTransform for text - fill the button
            RectTransform textRect = textGO.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = new Vector2(10, 10);
            textRect.offsetMax = new Vector2(-10, -10);

            // Add TextMeshPro component - appropriate text size
            TextMeshProUGUI textComponent = textGO.AddComponent<TextMeshProUGUI>();
            textComponent.text = text;
            textComponent.fontSize = 28; // Good size for medium buttons
            textComponent.color = new Color(1f, 1f, 1f, 1f); // Pure white text for maximum contrast
            textComponent.alignment = TextAlignmentOptions.Center;
            textComponent.fontStyle = TMPro.FontStyles.Bold;

            // Setup font
            SetupTextMeshProFont(textComponent);

            return buttonGO;
        }

        // Large button for pause menu
        private static GameObject CreateLargeButton(string text, Transform parent, Color buttonColor)
        {
            GameObject buttonGO = new GameObject(text);
            buttonGO.transform.SetParent(parent, false);

            // Add RectTransform component first
            RectTransform rect = buttonGO.AddComponent<RectTransform>();

            // Add LayoutElement to control sizing in vertical layout
            LayoutElement layoutElement = buttonGO.AddComponent<LayoutElement>();
            layoutElement.minHeight = 100;
            layoutElement.preferredHeight = 100;
            layoutElement.flexibleHeight = 0; // Don't expand vertically

            // Add Image component with proper color
            Image image = buttonGO.AddComponent<Image>();
            image.color = buttonColor;

            // Add Button component with better color transitions
            Button button = buttonGO.AddComponent<Button>();
            ColorBlock colors = button.colors;
            colors.normalColor = Color.white;
            colors.highlightedColor = new Color(1.2f, 1.2f, 1.2f, 1f); // Brighter highlight
            colors.pressedColor = new Color(0.8f, 0.8f, 0.8f, 1f);
            colors.selectedColor = new Color(1.15f, 1.15f, 1.15f, 1f);
            colors.disabledColor = new Color(0.5f, 0.5f, 0.5f, 1f);
            button.colors = colors;

            // Create text child
            GameObject textGO = new GameObject("Text");
            textGO.transform.SetParent(buttonGO.transform, false);

            // Add RectTransform for text - fill the button
            RectTransform textRect = textGO.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = new Vector2(15, 15);
            textRect.offsetMax = new Vector2(-15, -15);

            // Add TextMeshPro component - large text for main buttons
            TextMeshProUGUI textComponent = textGO.AddComponent<TextMeshProUGUI>();
            textComponent.text = text;
            textComponent.fontSize = 42; // Large font for main buttons
            textComponent.color = new Color(1f, 1f, 1f, 1f); // Pure white text for maximum contrast
            textComponent.alignment = TextAlignmentOptions.Center;
            textComponent.fontStyle = TMPro.FontStyles.Bold;

            // Setup font
            SetupTextMeshProFont(textComponent);

            return buttonGO;
        }

        // Overload for backward compatibility
        private static GameObject CreateButton(string text, Transform parent)
        {
            return CreateButton(text, parent, null);
        }

        private static GameObject CreateButton(string text, Transform parent, Color? buttonColor)
        {
            GameObject buttonGO = new GameObject(text);
            buttonGO.transform.SetParent(parent, false);

            // Add RectTransform component first
            RectTransform rect = buttonGO.AddComponent<RectTransform>();
            rect.sizeDelta = new Vector2(0, 45);

            // Add Image component with proper color
            Image image = buttonGO.AddComponent<Image>();
            image.color = buttonColor ?? new Color(0.25f, 0.25f, 0.35f, 1f); // Default dark blue-gray

            // Add Button component with color transitions
            Button button = buttonGO.AddComponent<Button>();
            ColorBlock colors = button.colors;
            colors.normalColor = Color.white;
            colors.highlightedColor = new Color(1.2f, 1.2f, 1.2f, 1f); // Slightly brighter
            colors.pressedColor = new Color(0.8f, 0.8f, 0.8f, 1f); // Slightly darker
            colors.selectedColor = new Color(1.1f, 1.1f, 1.1f, 1f);
            colors.disabledColor = new Color(0.5f, 0.5f, 0.5f, 1f);
            button.colors = colors;

            // Create text child
            GameObject textGO = new GameObject("Text");
            textGO.transform.SetParent(buttonGO.transform, false);

            // Add RectTransform for text
            RectTransform textRect = textGO.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = new Vector2(5, 5);
            textRect.offsetMax = new Vector2(-5, -5);

            // Add TextMeshPro component
            TextMeshProUGUI textComponent = textGO.AddComponent<TextMeshProUGUI>();
            textComponent.text = text;
            textComponent.fontSize = 16;
            textComponent.color = new Color(0.95f, 0.95f, 1f, 1f); // Light text
            textComponent.alignment = TextAlignmentOptions.Center;
            textComponent.fontStyle = TMPro.FontStyles.Bold;

            // Setup font (uses TextMeshPro defaults)
            SetupTextMeshProFont(textComponent);

            return buttonGO;
        }

        // Overload for backward compatibility
        private static GameObject CreateText(string text, Transform parent, float fontSize)
        {
            return CreateText(text, parent, fontSize, null);
        }

        private static GameObject CreateText(string text, Transform parent, float fontSize, Color? textColor)
        {
            GameObject textGO = new GameObject($"Text - {text}");
            textGO.transform.SetParent(parent, false);

            // Add RectTransform component first
            RectTransform rect = textGO.AddComponent<RectTransform>();

            // Add LayoutElement to control sizing in vertical layout
            LayoutElement layoutElement = textGO.AddComponent<LayoutElement>();
            layoutElement.minHeight = fontSize + 20;
            layoutElement.preferredHeight = fontSize + 20;
            layoutElement.flexibleHeight = 0; // Don't expand

            // Add TextMeshPro component
            TextMeshProUGUI textComponent = textGO.AddComponent<TextMeshProUGUI>();
            textComponent.text = text;
            textComponent.fontSize = fontSize;
            textComponent.color = textColor ?? new Color(1f, 1f, 1f, 1f); // Default pure white for maximum contrast
            textComponent.alignment = TextAlignmentOptions.Center;
            textComponent.fontStyle = TMPro.FontStyles.Bold;

            // Setup font (uses TextMeshPro defaults)
            SetupTextMeshProFont(textComponent);

            return textGO;
        }

        // Specific function for pause menu buttons with proper sizing
        private static GameObject CreatePauseMenuButton(string text, Transform parent, Color buttonColor)
        {
            GameObject buttonGO = new GameObject(text);
            buttonGO.transform.SetParent(parent, false);

            // Add RectTransform component
            RectTransform rect = buttonGO.AddComponent<RectTransform>();

            // Add LayoutElement with fixed height - this is key for proper spacing
            LayoutElement layoutElement = buttonGO.AddComponent<LayoutElement>();
            layoutElement.minHeight = 80;
            layoutElement.preferredHeight = 80;
            layoutElement.flexibleHeight = 0; // Don't expand

            // Add Image component
            Image image = buttonGO.AddComponent<Image>();
            image.color = buttonColor;

            // Add Button component
            Button button = buttonGO.AddComponent<Button>();
            ColorBlock colors = button.colors;
            colors.normalColor = Color.white;
            colors.highlightedColor = new Color(1.2f, 1.2f, 1.2f, 1f);
            colors.pressedColor = new Color(0.8f, 0.8f, 0.8f, 1f);
            colors.selectedColor = new Color(1.15f, 1.15f, 1.15f, 1f);
            colors.disabledColor = new Color(0.5f, 0.5f, 0.5f, 1f);
            button.colors = colors;

            // Create text child
            GameObject textGO = new GameObject("Text");
            textGO.transform.SetParent(buttonGO.transform, false);

            // Add RectTransform for text - fill the button
            RectTransform textRect = textGO.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;

            // Add TextMeshPro component
            TextMeshProUGUI textComponent = textGO.AddComponent<TextMeshProUGUI>();
            textComponent.text = text;
            textComponent.fontSize = 36;
            textComponent.color = new Color(1f, 1f, 1f, 1f);
            textComponent.alignment = TextAlignmentOptions.Center;
            textComponent.fontStyle = TMPro.FontStyles.Bold;

            // Setup font
            SetupTextMeshProFont(textComponent);

            return buttonGO;
        }

        // Specific function for pause menu text with proper sizing
        private static GameObject CreatePauseMenuText(string text, Transform parent, float fontSize)
        {
            GameObject textGO = new GameObject($"Text - {text}");
            textGO.transform.SetParent(parent, false);

            // Add RectTransform component
            RectTransform rect = textGO.AddComponent<RectTransform>();

            // Add LayoutElement with fixed height
            LayoutElement layoutElement = textGO.AddComponent<LayoutElement>();
            layoutElement.minHeight = fontSize + 20;
            layoutElement.preferredHeight = fontSize + 20;
            layoutElement.flexibleHeight = 0; // Don't expand

            // Add TextMeshPro component
            TextMeshProUGUI textComponent = textGO.AddComponent<TextMeshProUGUI>();
            textComponent.text = text;
            textComponent.fontSize = fontSize;
            textComponent.color = new Color(1f, 1f, 1f, 1f);
            textComponent.alignment = TextAlignmentOptions.Center;
            textComponent.fontStyle = TMPro.FontStyles.Bold;

            // Setup font
            SetupTextMeshProFont(textComponent);

            return textGO;
        }

        // Helper function to create spacers with fixed height
        private static GameObject CreateSpacer(Transform parent, float height)
        {
            GameObject spacer = new GameObject("Spacer");
            spacer.transform.SetParent(parent, false);

            RectTransform rect = spacer.AddComponent<RectTransform>();

            LayoutElement layoutElement = spacer.AddComponent<LayoutElement>();
            layoutElement.minHeight = height;
            layoutElement.preferredHeight = height;
            layoutElement.flexibleHeight = 0; // Don't expand

            return spacer;
        }

        private static void AssignReferences(SimpleMenUISystem menUISystem, GameObject overlay, GameObject pauseMenu, GameObject settingsPanel)
        {
            SerializedObject serializedSystem = new SerializedObject(menUISystem);

            // Assign panels
            serializedSystem.FindProperty("overlayPanel").objectReferenceValue = overlay;
            serializedSystem.FindProperty("pauseMenuPanel").objectReferenceValue = pauseMenu;
            serializedSystem.FindProperty("settingsPanel").objectReferenceValue = settingsPanel;

            // Assign pause menu buttons
            serializedSystem.FindProperty("resumeButton").objectReferenceValue = pauseMenu.transform.Find("Resume")?.GetComponent<Button>();
            serializedSystem.FindProperty("settingsButton").objectReferenceValue = pauseMenu.transform.Find("Settings")?.GetComponent<Button>();
            serializedSystem.FindProperty("exitButton").objectReferenceValue = pauseMenu.transform.Find("Exit")?.GetComponent<Button>();

            // Assign settings buttons
            Transform categoryTabs = settingsPanel.transform.Find("Category Tabs");
            serializedSystem.FindProperty("backButton").objectReferenceValue = categoryTabs?.Find("Back")?.GetComponent<Button>();
            serializedSystem.FindProperty("graphicsButton").objectReferenceValue = categoryTabs?.Find("Graphics")?.GetComponent<Button>();
            serializedSystem.FindProperty("audioButton").objectReferenceValue = categoryTabs?.Find("Audio")?.GetComponent<Button>();
            serializedSystem.FindProperty("controlsButton").objectReferenceValue = categoryTabs?.Find("Controls")?.GetComponent<Button>();
            serializedSystem.FindProperty("gameplayButton").objectReferenceValue = categoryTabs?.Find("Gameplay")?.GetComponent<Button>();
            serializedSystem.FindProperty("debugButton").objectReferenceValue = categoryTabs?.Find("Debug")?.GetComponent<Button>();

            // Assign settings panels
            Transform contentArea = settingsPanel.transform.Find("Content Area");
            serializedSystem.FindProperty("graphicsPanel").objectReferenceValue = contentArea?.Find("Graphics Settings Panel")?.gameObject;
            serializedSystem.FindProperty("audioPanel").objectReferenceValue = contentArea?.Find("Audio Settings Panel")?.gameObject;
            serializedSystem.FindProperty("controlsPanel").objectReferenceValue = contentArea?.Find("Controls Settings Panel")?.gameObject;
            serializedSystem.FindProperty("gameplayPanel").objectReferenceValue = contentArea?.Find("Gameplay Settings Panel")?.gameObject;
            serializedSystem.FindProperty("debugPanel").objectReferenceValue = contentArea?.Find("Debug Settings Panel")?.gameObject;

            serializedSystem.ApplyModifiedProperties();

            Debug.Log("All references assigned successfully!");
        }
    }
}
