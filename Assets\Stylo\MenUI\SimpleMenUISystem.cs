using UnityEngine;
using UnityEngine.UI;
using UnityEngine.InputSystem;
using TMPro;

namespace Stylo.MenUI
{
    /// <summary>
    /// Simple, streamlined pause menu system that just works.
    /// No over-engineering, no complex initialization - just direct, reliable functionality.
    /// </summary>
    public class SimpleMenUISystem : MonoBehaviour
    {
        [Header("UI Panels")]
        [SerializeField] private GameObject pauseMenuPanel;
        [SerializeField] private GameObject settingsPanel;
        [SerializeField] private GameObject overlayPanel;

        [<PERSON><PERSON>("Pause Menu Buttons")]
        [SerializeField] private Button resumeButton;
        [SerializeField] private Button settingsButton;
        [SerializeField] private Button exitButton;

        [<PERSON><PERSON>("Settings Buttons")]
        [SerializeField] private Button backButton;

        [Header("Settings Category Buttons")]
        [SerializeField] private Button graphicsButton;
        [SerializeField] private Button audioButton;
        [SerializeField] private But<PERSON> controlsButton;
        [SerializeField] private Button gameplayButton;
        [SerializeField] private Button debugButton;

        [Head<PERSON>("Settings Panels")]
        [SerializeField] private GameObject graphicsPanel;
        [SerializeField] private GameObject audioPanel;
        [SerializeField] private GameObject controlsPanel;
        [SerializeField] private GameObject gameplayPanel;
        [SerializeField] private GameObject debugPanel;

        [Header("Configuration")]
        [SerializeField] private bool enableDebugMode = true;
        [SerializeField] private bool pauseAudio = true;
        [SerializeField] private Color overlayColor = new Color(0f, 0f, 0f, 0.7f);

        // Input system
        private DefaultControls _controls;
        private InputAction _pauseAction;

        // State
        private bool _isPaused = false;
        private float _previousTimeScale = 1f;
        private GameObject _currentSettingsPanel;

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeInput();
            SetupButtons();
        }

        private void Start()
        {
            // Hide all panels initially
            HideAllPanels();
        }

        private void OnEnable()
        {
            if (_controls != null)
            {
                _controls.Enable();
                _pauseAction.performed += OnPauseInput;
            }
        }

        private void OnDisable()
        {
            if (_controls != null)
            {
                _pauseAction.performed -= OnPauseInput;
                _controls.Disable();
            }
        }

        private void OnDestroy()
        {
            _controls?.Dispose();
        }

        #endregion

        #region Initialization

        private void InitializeInput()
        {
            _controls = new DefaultControls();
            _pauseAction = _controls.UI.Pause;
        }

        private void SetupButtons()
        {
            // Pause menu buttons
            if (resumeButton != null)
                resumeButton.onClick.AddListener(OnResumeClicked);

            if (settingsButton != null)
                settingsButton.onClick.AddListener(OnSettingsClicked);

            if (exitButton != null)
                exitButton.onClick.AddListener(OnExitClicked);

            // Settings buttons
            if (backButton != null)
                backButton.onClick.AddListener(OnBackClicked);

            // Category buttons
            if (graphicsButton != null)
                graphicsButton.onClick.AddListener(() => ShowSettingsCategory(graphicsPanel));

            if (audioButton != null)
                audioButton.onClick.AddListener(() => ShowSettingsCategory(audioPanel));

            if (controlsButton != null)
                controlsButton.onClick.AddListener(() => ShowSettingsCategory(controlsPanel));

            if (gameplayButton != null)
                gameplayButton.onClick.AddListener(() => ShowSettingsCategory(gameplayPanel));

            if (debugButton != null)
                debugButton.onClick.AddListener(() => ShowSettingsCategory(debugPanel));

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: All buttons setup complete");
        }

        private void HideAllPanels()
        {
            if (pauseMenuPanel != null) pauseMenuPanel.SetActive(false);
            if (settingsPanel != null) settingsPanel.SetActive(false);
            if (overlayPanel != null) overlayPanel.SetActive(false);

            // Hide all settings category panels
            if (graphicsPanel != null) graphicsPanel.SetActive(false);
            if (audioPanel != null) audioPanel.SetActive(false);
            if (controlsPanel != null) controlsPanel.SetActive(false);
            if (gameplayPanel != null) gameplayPanel.SetActive(false);
            if (debugPanel != null) debugPanel.SetActive(false);
        }

        #endregion

        #region Input Handling

        private void OnPauseInput(InputAction.CallbackContext context)
        {
            TogglePause();
        }

        #endregion

        #region Button Callbacks

        private void OnResumeClicked()
        {
            HidePause();
        }

        private void OnSettingsClicked()
        {
            ShowSettings();
        }

        private void OnExitClicked()
        {
            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Exit button clicked");

#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#else
            Application.Quit();
#endif
        }

        private void OnBackClicked()
        {
            HideSettings();
        }

        #endregion

        #region Public API

        public void TogglePause()
        {
            if (_isPaused)
                HidePause();
            else
                ShowPause();
        }

        public void ShowPause()
        {
            if (_isPaused) return;

            _isPaused = true;

            // Show overlay
            if (overlayPanel != null)
                overlayPanel.SetActive(true);

            // Show pause menu
            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(true);

            // Pause systems
            PauseAllSystems();

            // Set focus
            if (resumeButton != null)
                resumeButton.Select();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Pause menu shown");
        }

        public void HidePause()
        {
            if (!_isPaused) return;

            _isPaused = false;

            // Hide all panels
            HideAllPanels();

            // Resume systems
            ResumeAllSystems();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Pause menu hidden");
        }

        public void ShowSettings()
        {
            // Hide pause menu
            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(false);

            // Show settings panel
            if (settingsPanel != null)
                settingsPanel.SetActive(true);

            // Show first category (Graphics) by default
            ShowSettingsCategory(graphicsPanel);

            // Set focus to first category button
            if (graphicsButton != null)
                graphicsButton.Select();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Settings panel shown");
        }

        public void HideSettings()
        {
            // Hide settings panel
            if (settingsPanel != null)
                settingsPanel.SetActive(false);

            // Show pause menu
            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(true);

            // Set focus back to settings button
            if (settingsButton != null)
                settingsButton.Select();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: Settings panel hidden");
        }

        public void ShowSettingsCategory(GameObject categoryPanel)
        {
            // Hide current category panel
            if (_currentSettingsPanel != null)
                _currentSettingsPanel.SetActive(false);

            // Show new category panel
            if (categoryPanel != null)
            {
                categoryPanel.SetActive(true);
                _currentSettingsPanel = categoryPanel;

                if (enableDebugMode)
                    Debug.Log($"SimpleMenUISystem: Showing settings category: {categoryPanel.name}");
            }
        }

        #endregion

        #region System Control

        private void PauseAllSystems()
        {
            // Store current time scale and pause Unity
            _previousTimeScale = Time.timeScale;
            Time.timeScale = 0f;

            // Pause audio
            if (pauseAudio)
                AudioListener.pause = true;

            // Pause Epoch time system
            if (TimeManager.Instance != null)
                TimeManager.Instance.PauseTime();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: All systems paused");
        }

        private void ResumeAllSystems()
        {
            // Restore time scale
            Time.timeScale = _previousTimeScale;

            // Resume audio
            if (pauseAudio)
                AudioListener.pause = false;

            // Resume Epoch time system
            if (TimeManager.Instance != null)
                TimeManager.Instance.ResumeTime();

            if (enableDebugMode)
                Debug.Log("SimpleMenUISystem: All systems resumed");
        }

        #endregion
    }
}
