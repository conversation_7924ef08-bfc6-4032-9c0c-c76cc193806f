using System;
using System.Collections.Generic;
using System.Linq;
using Unity.Cinemachine;
// REMOVED: using Michsky.UI.Reach; - Replaced with custom UI system
using PrimeTween;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.SceneManagement;
using BTR;
using ZLinq;
using Stylo.MenUI;

namespace BTR
{
    [DefaultExecutionOrder(-150)]
    public class GameManager : MonoBehaviour
    {
        public static GameManager Instance { get; private set; }

        [Header("Player Reference")]
        [SerializeField]
        private PlayerMovement playerMovement;
        private PlayerHealth playerHealth;

        [SerializeField]
        private bool isPlayerInvincible = false;

        [Header("Dependencies")]
        public EnemyManager enemyManager;

        [SerializeField]
        private SimpleMenUISystem pauseMenuManager; // Updated to use new Simple MenUI system

        private CinemachineStateDrivenCamera stateDrivenCamera;
        private DebugSettings debugSettings;
        private bool isPlayerDead = false;

        private List<Transform> spawnedEnemies = new List<Transform>();
        private Dictionary<Transform, bool> lockedEnemies = new Dictionary<Transform, bool>();

        public int totalPlayerProjectilesShot = 0;
        public int playerProjectileHits = 0;

        public ScoreManager ScoreManager { get; private set; }
        public AudioManager AudioManager { get; private set; }
        public TimeManager TimeManager { get; private set; }

        private PlayerUI playerUI;

        [Header("Dependencies")]
        [SerializeField] private AudioManager audioManager;

        private GameEvents gameEvents;
        private SceneEvents sceneEvents => SceneEventsManager.Instance.Events;

        private HashSet<string> playerLocks = new HashSet<string>();

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                SceneManager.sceneLoaded += OnSceneLoaded;

                InitializeManagers();

                // Get GameEvents from GameEventsManager with detailed error checking
                var eventsManager = GameEventsManager.Instance;
                if (eventsManager == null)
                {
                    Debug.LogError($"[{nameof(GameManager)}] GameEventsManager.Instance is null! Make sure GameEventsManager prefab is in the scene.");
                    return;
                }

                gameEvents = eventsManager.Events;
                if (gameEvents == null)
                {
                    Debug.LogError($"[{nameof(GameManager)}] GameEvents asset is null on GameEventsManager! Check the Events field in the inspector.");
                    return;
                }

                SubscribeToEvents();
                PrimeTweenConfig.SetTweensCapacity(1600);
                InitializePlayerHealth();

                // Defer Resources.Load to async initialization to avoid blocking startup
                StartCoroutine(InitializeDebugSettingsAsync());

                // Defer PlayerUI lookup to async initialization
                StartCoroutine(InitializePlayerUIAsync());
            }
            else if (Instance != this)
            {
                Destroy(gameObject);
                return;
            }
        }

        private void InitializeManagers()
        {
            // Get components on same GameObject immediately (fast)
            ScoreManager = GetComponent<ScoreManager>();
            TimeManager = GetComponent<TimeManager>();

            // Defer expensive FindObjectOfType calls to async coroutine
            StartCoroutine(InitializeManagersAsync());
        }

        private System.Collections.IEnumerator InitializeManagersAsync()
        {
            // Wait one frame to avoid blocking startup
            yield return null;

            // Find AudioManager asynchronously
            AudioManager = FindAnyObjectByType<AudioManager>(FindObjectsInactive.Include);
            if (AudioManager == null)
            {
                Debug.LogError($"[{GetType().Name}] AudioManager not found in scene!");
            }

            // Wait another frame before next expensive operation
            yield return null;

            // Find EnemyManager asynchronously
            if (enemyManager == null)
            {
                enemyManager = FindFirstObjectByType<EnemyManager>(FindObjectsInactive.Include);
                if (enemyManager == null)
                {
                    Debug.LogError($"[{GetType().Name}] EnemyManager not found in scene!");
                }
            }

            // Validate required components
            if (ScoreManager == null || TimeManager == null)
            {
                Debug.LogError($"[{GetType().Name}] One or more required manager components are missing on GameManager object!");
            }

            Debug.Log($"[{GetType().Name}] Async manager initialization completed");
        }

        private System.Collections.IEnumerator InitializePlayerUIAsync()
        {
            // Wait one frame to avoid blocking startup
            yield return null;

            playerUI = FindAnyObjectByType<PlayerUI>(FindObjectsInactive.Include);
            if (playerUI == null)
            {
                Debug.LogError($"[{GetType().Name}] PlayerUI not found in the scene.");
            }
            else
            {
                Debug.Log($"[{GetType().Name}] PlayerUI initialized asynchronously");
            }
        }

        private System.Collections.IEnumerator InitializeDebugSettingsAsync()
        {
            // Wait one frame to avoid blocking startup
            yield return null;

            debugSettings = Resources.Load<DebugSettings>("DebugSettings");
            if (debugSettings == null)
            {
                Debug.LogError($"[{GetType().Name}] DebugSettings asset not found. Create it in Resources folder.");
            }
            else
            {
                TimeManager.SetDebugSettings(debugSettings);
                Debug.Log($"[{GetType().Name}] DebugSettings loaded asynchronously");
            }
        }

        private void SubscribeToEvents()
        {
            if (gameEvents == null)
            {
                Debug.LogError("GameEvents not found! Make sure GameEventsManager is in the scene with assigned GameEvents asset.");
                return;
            }

            gameEvents.OnGameRestarted += HandleGameRestart;
            gameEvents.OnPlayerDied += HandlePlayerDeath;
            gameEvents.OnSceneInitializationRequested += InitializeListenersAndComponents;
            gameEvents.OnPlayerInvincibilityChanged += SetPlayerInvincibility;
            gameEvents.OnGameOver += OnGameOver;
            gameEvents.OnSceneTransitionStarted += OnSceneTransitionStarted;
            gameEvents.OnSceneTransitionCompleted += OnSceneTransitionCompleted;
            gameEvents.OnWaveCountUpdated += HandleWaveCountUpdated;
        }

        private void UnsubscribeFromEvents()
        {
            // Skip if gameEvents was never initialized
            if (gameEvents == null) return;

            gameEvents.OnGameRestarted -= HandleGameRestart;
            gameEvents.OnPlayerDied -= HandlePlayerDeath;
            gameEvents.OnSceneInitializationRequested -= InitializeListenersAndComponents;
            gameEvents.OnPlayerInvincibilityChanged -= SetPlayerInvincibility;
            gameEvents.OnGameOver -= OnGameOver;
            gameEvents.OnSceneTransitionStarted -= OnSceneTransitionStarted;
            gameEvents.OnSceneTransitionCompleted -= OnSceneTransitionCompleted;
            gameEvents.OnWaveCountUpdated -= HandleWaveCountUpdated;
        }

        private void InitializePlayerHealth()
        {
            // Defer expensive FindObjectOfType call to async coroutine
            StartCoroutine(InitializePlayerHealthAsync());
        }

        private System.Collections.IEnumerator InitializePlayerHealthAsync()
        {
            if (playerHealth == null)
            {
                // Wait one frame to avoid blocking startup
                yield return null;

                playerHealth = FindFirstObjectByType<PlayerHealth>(FindObjectsInactive.Include);
                if (playerHealth == null)
                {
                    Debug.LogError($"[{GetType().Name}] PlayerHealth component not found in the scene.");
                }
                else
                {
                    Debug.Log($"[{GetType().Name}] PlayerHealth initialized asynchronously");
                }
            }
        }

        private void OnEnable()
        {
            // Only subscribe if we're not the singleton instance (main GameManager)
            if (Instance != this)
            {
                var eventsManager = GameEventsManager.Instance;
                if (eventsManager != null && eventsManager.Events != null)
                {
                    gameEvents = eventsManager.Events;
                    SubscribeToEvents();
                }
            }
        }

        private void OnDisable()
        {
            UnsubscribeFromEvents();
        }

        private void OnSceneLoaded(UnityEngine.SceneManagement.Scene scene, UnityEngine.SceneManagement.LoadSceneMode mode)
        {
            gameEvents.TriggerSceneInitializationRequested(scene, mode);
        }

        private void OnDestroy()
        {
            if (Instance == this)
            {
                SceneManager.sceneLoaded -= OnSceneLoaded;
            }
        }

        private void Start()
        {
            TimeManager.InitializeDebugTimeScale();
            gameEvents.TriggerGameStarted();
        }

        public void InitializeListenersAndComponents(UnityEngine.SceneManagement.Scene scene, UnityEngine.SceneManagement.LoadSceneMode mode)
        {
            UnityMainThreadDispatcher
                .Instance()
                .Enqueue(() =>
                {
                    InitializeCameraSwitching();
                    InitializeCrosshair();
                    InitializeShooterMovement();

                    stateDrivenCamera = FindFirstObjectByType<CinemachineStateDrivenCamera>(FindObjectsInactive.Include);
                    if (stateDrivenCamera == null)
                    {
                        Debug.LogError($"[{GetType().Name}] No Cinemachine State Driven Camera found in the scene.");
                    }
                });
        }

        private void InitializeCameraSwitching()
        {
            var cameraSwitching = FindFirstObjectByType<CinemachineCameraSwitching>(FindObjectsInactive.Include);
            if (cameraSwitching != null)
            {
                gameEvents.OnSceneTransitionStarted += cameraSwitching.SwitchToTransitionCamera;
                gameEvents.OnSceneTransitionCompleted += () => cameraSwitching.SwitchToTransitionCamera();
            }
            else
            {
                Debug.LogError($"[{GetType().Name}] CinemachineCameraSwitching component not found in the scene.");
            }
        }

        private void InitializeCrosshair()
        {
            if (PlayerLocking.Instance != null)
            {
                gameEvents.OnSceneTransitionStarted += () => PlayerLocking.Instance.ReleasePlayerLocks();
            }
        }

        private void InitializeShooterMovement()
        {
            var shooterMovement = FindFirstObjectByType<ShooterMovement>(FindObjectsInactive.Include);
            if (shooterMovement != null)
            {
                gameEvents.OnSceneTransitionCompleted += () => shooterMovement.ResetToCenter();
            }
            else
            {
                Debug.LogError($"[{GetType().Name}] ShooterMovement component not found in the scene for transCamOff.");
            }
        }

        private void HandlePlayerDeath()
        {
            if (isPlayerDead) return;
            isPlayerDead = true;

            gameEvents.TriggerPlayerDied();

            if (pauseMenuManager != null)
            {
                // Show pause menu when player dies
                pauseMenuManager.ShowPause();
            }
            else
            {
                Debug.LogWarning("GameManager: MenUI System not assigned - cannot show pause menu on player death");
            }
        }

        public async void RestartGame()
        {
            isPlayerDead = false;
            gameEvents.TriggerGameRestart();

            if (LoadingScreen.Instance != null)
            {
                await LoadingScreen.Instance.StartFadeIn();
            }

            HandleGameRestart();
        }

        private void HandleGameRestart()
        {
            isPlayerDead = false;
            if (pauseMenuManager != null)
            {
                // Hide pause menu when game restarts
                pauseMenuManager.HidePause();
            }

            // Clear any remaining enemies and projectiles
            var killer = FindFirstObjectByType<EnemyKiller>(FindObjectsInactive.Include);
            if (killer != null)
            {
                killer.KillAllEnemies();
            }
            else
            {
                Debug.LogError("EnemyKiller not found! Ensure Debug/EnemyKiller script is in the scene for enemy cleanup");
            }
            ProjectileManager.Instance.ClearAllProjectiles();

            // Reset time scale first
            TimeManager.ResetTimeScale();

            // Reset player state and score
            EnableGameplayElements();
            if (ScoreManager != null)
            {
                ScoreManager.ResetScore();
            }
            else
            {
                Debug.LogError("ScoreManager is null when trying to reset score!");
            }

            // Subscribe to Enemy Events
            gameEvents.OnEnemyDeath += (transform) =>
            {
                if (ScoreManager != null)
                {
                    ScoreManager.OnEnemyKilled();
                }
            };

            // Subscribe to Player Hit Events
            gameEvents.OnPlayerDamaged += (damage) =>
            {
                if (ScoreManager != null)
                {
                    ScoreManager.ReportDamage(Mathf.RoundToInt(damage));
                }
            };

            // Reset invincibility state
            SetPlayerInvincibility(false);
        }

        private void DisableGameplayElements()
        {
            if (playerMovement != null)
                playerMovement.enabled = false;

            var killer = FindFirstObjectByType<EnemyKiller>(FindObjectsInactive.Include);
            if (killer != null)
            {
                killer.KillAllEnemies();
            }
            else
            {
                Debug.LogError("EnemyKiller not found! Ensure Debug/EnemyKiller script is in the scene for enemy cleanup");
            }
            ProjectileManager.Instance.ClearAllProjectiles();
        }

        private void EnableGameplayElements()
        {
            if (playerMovement != null)
                playerMovement.enabled = true;
        }

        public void SetPlayerInvincibility(bool isInvincible)
        {
            if (isPlayerInvincible != isInvincible)
            {
                isPlayerInvincible = isInvincible;
                if (playerHealth != null)
                {
                    playerHealth.SetInvincibleInternal(isInvincible);
                }
                else
                {
                    Debug.LogError($"[{GetType().Name}] PlayerHealth is not set in GameManager.");
                }
            }
        }

        public bool IsPlayerInvincible()
        {
            return isPlayerInvincible;
        }

        public void RegisterEnemy(Transform enemy)
        {
            if (!spawnedEnemies.Contains(enemy))
            {
                spawnedEnemies.Add(enemy);
                lockedEnemies[enemy] = false;
                EnemyEvents.TriggerEnemyRegistered(enemy);
                Debug.Log($"[{GetType().Name}] Enemy registered: {enemy.name}");
            }
            else
            {
                Debug.LogWarning($"[{GetType().Name}] Enemy already registered: {enemy.name}");
            }
        }

        public void UnregisterEnemy(Transform enemy)
        {
            if (spawnedEnemies.Contains(enemy))
            {
                spawnedEnemies.Remove(enemy);
                lockedEnemies.Remove(enemy);
                EnemyEvents.TriggerEnemyDespawned(enemy);
                Debug.Log($"[{GetType().Name}] Enemy unregistered: {enemy.name}");
            }
        }

        public void SetEnemyLockState(Transform enemy, bool isLocked)
        {
            if (lockedEnemies.ContainsKey(enemy) && lockedEnemies[enemy] != isLocked)
            {
                lockedEnemies[enemy] = isLocked;
                EnemyEvents.TriggerEnemyLockStateChanged(enemy);
                Debug.Log($"[{GetType().Name}] Enemy lock state changed: {enemy.name} - {isLocked}");
            }
        }

        public void ClearAllEnemyLocks()
        {
            List<Transform> enemiesToRemove = new List<Transform>();

            foreach (var enemy in lockedEnemies.Keys)
            {
                if (enemy == null)
                {
                    enemiesToRemove.Add(enemy);
                    continue;
                }

                SetEnemyLockState(enemy, false);
            }

            foreach (var enemy in enemiesToRemove)
            {
                lockedEnemies.Remove(enemy);
            }

            Debug.Log($"[{GetType().Name}] All enemy locks cleared");
        }

        public void ClearSpawnedEnemies()
        {
            spawnedEnemies.Clear();
            lockedEnemies.Clear();
        }

        public void RemoveDestroyedEnemies()
        {
            spawnedEnemies.RemoveAll(enemy => enemy == null);
            var destroyedEnemies = lockedEnemies.Keys.AsValueEnumerable().Where(enemy => enemy == null).ToList();
            foreach (var enemy in destroyedEnemies)
            {
                lockedEnemies.Remove(enemy);
            }
        }

        [System.Obsolete("KillAllEnemies is deprecated. Use EnemySystem/Debug/EnemyKiller instead.")]
        public void KillAllEnemies()
        {
            Debug.LogWarning("[Deprecation] GameManager.KillAllEnemies() called - redirecting to EnemyKiller");

            var killer = FindFirstObjectByType<EnemyKiller>(FindObjectsInactive.Include);
            if (killer != null)
            {
                Debug.Log("Found EnemyKiller instance, executing kill command");
                killer.KillAllEnemies();
            }
            else
            {
                Debug.LogError("EnemyKiller not found! Ensure Debug/EnemyKiller script is in the scene for enemy cleanup");
            }
        }

        public void LogProjectileHit(bool isPlayerShot, bool hitEnemy, string additionalInfo = "")
        {
            if (isPlayerShot && hitEnemy)
            {
                playerProjectileHits++;
            }

            string message = isPlayerShot
                ? (hitEnemy ? "Player projectile hit enemy" : "Player projectile missed")
                : (hitEnemy ? "Enemy projectile hit player" : "Enemy projectile missed");

            if (!string.IsNullOrEmpty(additionalInfo))
            {
                message += $" - {additionalInfo}";
            }

            Debug.Log($"[{GetType().Name}] [ProjectileHit] {message}");
        }

        public void LogProjectileExpired(bool isPlayerShot)
        {
            string message = isPlayerShot ? "Player projectile expired" : "Enemy projectile expired";
            Debug.Log($"[{GetType().Name}] [ProjectileExpired] {message}");
        }

        private void OnGameOver()
        {
            isPlayerDead = true;
            // Additional game over logic
        }

        private void OnSceneTransitionStarted()
        {
            if (PlayerLocking.Instance != null)
            {
                PlayerLocking.Instance.ReleasePlayerLocks();
            }
        }

        private void OnSceneTransitionCompleted()
        {
            var shooterMovement = FindFirstObjectByType<ShooterMovement>();
            if (shooterMovement != null)
            {
                shooterMovement.ResetToCenter();
            }
        }

        private void HandleWaveCountUpdated(int count)
        {
            // Handle wave count update logic
            gameEvents.TriggerWaveCountUpdated(count);
        }

        public bool IsGamePaused()
        {
            return Time.timeScale == 0f;
        }

        public bool IsGameOver()
        {
            return isPlayerDead;
        }

        public void StartGame()
        {
            if (gameEvents != null)
            {
                gameEvents.TriggerGameStarted();
            }
        }

        public void AddPlayerLock(string lockId)
        {
            playerLocks.Add(lockId);
            UpdatePlayerMovementState();
        }

        public void RemovePlayerLock(string lockId)
        {
            playerLocks.Remove(lockId);
            UpdatePlayerMovementState();
        }

        public void ClearAllPlayerLocks()
        {
            playerLocks.Clear();
            UpdatePlayerMovementState();
        }

        private void UpdatePlayerMovementState()
        {
            if (playerMovement != null)
            {
                playerMovement.enabled = playerLocks.Count == 0;
            }
        }
    }
}
