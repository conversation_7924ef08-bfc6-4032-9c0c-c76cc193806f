using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace Stylo.MenUI
{
    /// <summary>
    /// Helper script to programmatically create the MenUI pause menu structure.
    /// This can be used to set up the pause menu UI in the editor or at runtime.
    /// </summary>
    public static class MenUISetupHelper
    {
        /// <summary>
        /// Creates a complete pause menu UI structure under the specified parent canvas
        /// </summary>
        /// <param name="parentCanvas">The canvas to create the pause menu under</param>
        /// <returns>The created MenUISystem component</returns>
        public static MenUISystem CreatePauseMenuUI(Canvas parentCanvas)
        {
            if (parentCanvas == null)
            {
                Debug.LogError("MenUISetupHelper: Parent canvas is null!");
                return null;
            }

            // Ensure Canvas has proper UI interaction components
            SetupCanvasForUIInteraction(parentCanvas);

            // Create main MenUI System GameObject
            GameObject menUISystemGO = new GameObject("MenUI System");
            menUISystemGO.transform.SetParent(parentCanvas.transform, false);

            // Add MenUISystem component
            MenUISystem menUISystem = menUISystemGO.AddComponent<MenUISystem>();

            // Create overlay panel first (behind everything)
            GameObject overlayPanel = CreateOverlayPanel(menUISystemGO.transform);
            overlayPanel.SetActive(false); // Start deactivated

            // Create pause menu panel (initially inactive for proper editor behavior)
            GameObject pauseMenuPanel = CreatePauseMenuPanel(menUISystemGO.transform);
            pauseMenuPanel.SetActive(false); // Start deactivated

            // Create buttons
            GameObject resumeButton = CreateButton(pauseMenuPanel.transform, "ResumeButton", "RESUME", new Vector2(0, 50));
            GameObject settingsButton = CreateButton(pauseMenuPanel.transform, "SettingsButton", "SETTINGS", new Vector2(0, 0));
            GameObject exitButton = CreateButton(pauseMenuPanel.transform, "ExitButton", "EXIT", new Vector2(0, -50));

            // Apply cyberpunk styling
            ApplyCyberpunkStyling(pauseMenuPanel, resumeButton, settingsButton, exitButton);

            // Configure MenUISystem references via reflection (since fields are private)
            var pauseMenuPanelField = typeof(MenUISystem).GetField("pauseMenuPanel",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var overlayPanelField = typeof(MenUISystem).GetField("overlayPanel",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var resumeButtonField = typeof(MenUISystem).GetField("resumeButton",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var settingsButtonField = typeof(MenUISystem).GetField("settingsButton",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var exitButtonField = typeof(MenUISystem).GetField("exitButton",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var pauseCanvasField = typeof(MenUISystem).GetField("pauseCanvas",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            pauseMenuPanelField?.SetValue(menUISystem, pauseMenuPanel);
            overlayPanelField?.SetValue(menUISystem, overlayPanel);
            resumeButtonField?.SetValue(menUISystem, resumeButton.GetComponent<Button>());
            settingsButtonField?.SetValue(menUISystem, settingsButton.GetComponent<Button>());
            exitButtonField?.SetValue(menUISystem, exitButton.GetComponent<Button>());
            pauseCanvasField?.SetValue(menUISystem, parentCanvas);

            // Ensure EventSystem exists for UI interaction
            EnsureEventSystemExists();

            Debug.Log("MenUISetupHelper: Pause menu UI created successfully with proper interaction support!");
            return menUISystem;
        }

        /// <summary>
        /// Ensures the Canvas has all necessary components for UI interaction
        /// </summary>
        /// <param name="canvas">The canvas to configure</param>
        private static void SetupCanvasForUIInteraction(Canvas canvas)
        {
            // Ensure Canvas has GraphicRaycaster for mouse interaction
            if (canvas.GetComponent<UnityEngine.UI.GraphicRaycaster>() == null)
            {
                canvas.gameObject.AddComponent<UnityEngine.UI.GraphicRaycaster>();
                Debug.Log("MenUISetupHelper: Added GraphicRaycaster to Canvas for mouse interaction");
            }

            // Ensure Canvas has CanvasScaler for proper scaling
            if (canvas.GetComponent<UnityEngine.UI.CanvasScaler>() == null)
            {
                var scaler = canvas.gameObject.AddComponent<UnityEngine.UI.CanvasScaler>();
                scaler.uiScaleMode = UnityEngine.UI.CanvasScaler.ScaleMode.ScaleWithScreenSize;
                scaler.referenceResolution = new Vector2(1920, 1080);
                scaler.screenMatchMode = UnityEngine.UI.CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
                scaler.matchWidthOrHeight = 0.5f;
                Debug.Log("MenUISetupHelper: Added CanvasScaler to Canvas for proper scaling");
            }

            // Configure Canvas settings for overlay UI
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvas.sortingOrder = 100; // High sort order for pause menu
        }

        /// <summary>
        /// Ensures an EventSystem exists in the scene for UI interaction
        /// </summary>
        private static void EnsureEventSystemExists()
        {
            if (UnityEngine.EventSystems.EventSystem.current == null)
            {
                GameObject eventSystemGO = new GameObject("EventSystem");
                eventSystemGO.AddComponent<UnityEngine.EventSystems.EventSystem>();

#if ENABLE_INPUT_SYSTEM
                eventSystemGO.AddComponent<UnityEngine.InputSystem.UI.InputSystemUIInputModule>();
#else
                    eventSystemGO.AddComponent<UnityEngine.EventSystems.StandaloneInputModule>();
#endif

                Debug.Log("MenUISetupHelper: Created EventSystem for UI interaction");
            }
        }

        private static GameObject CreateOverlayPanel(Transform parent)
        {
            GameObject overlay = new GameObject("OverlayPanel");
            overlay.transform.SetParent(parent, false);

            // Add Image component for the overlay background
            Image overlayImage = overlay.AddComponent<Image>();
            overlayImage.color = new Color(0f, 0f, 0f, 0.7f); // Default dark overlay

            // Configure RectTransform for full screen coverage
            RectTransform rectTransform = overlay.GetComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.sizeDelta = Vector2.zero;
            rectTransform.anchoredPosition = Vector2.zero;

            // Set as first child so it renders behind everything else
            overlay.transform.SetAsFirstSibling();

            return overlay;
        }

        private static GameObject CreatePauseMenuPanel(Transform parent)
        {
            GameObject panel = new GameObject("PauseMenuPanel");
            panel.transform.SetParent(parent, false);

            // Add Image component for background
            Image panelImage = panel.AddComponent<Image>();
            panelImage.color = new Color(0.05f, 0.05f, 0.1f, 0.9f); // Dark blue-black with transparency

            // Add RectTransform and configure
            RectTransform rectTransform = panel.GetComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.sizeDelta = Vector2.zero;
            rectTransform.anchoredPosition = Vector2.zero;

            // Add VerticalLayoutGroup for button arrangement
            VerticalLayoutGroup layoutGroup = panel.AddComponent<VerticalLayoutGroup>();
            layoutGroup.childAlignment = TextAnchor.MiddleCenter;
            layoutGroup.spacing = 20f;
            layoutGroup.padding = new RectOffset(50, 50, 50, 50);

            // Add ContentSizeFitter
            ContentSizeFitter sizeFitter = panel.AddComponent<ContentSizeFitter>();
            sizeFitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;

            return panel;
        }

        private static GameObject CreateButton(Transform parent, string name, string text, Vector2 position)
        {
            GameObject buttonGO = new GameObject(name);
            buttonGO.transform.SetParent(parent, false);

            // Add Image component
            Image buttonImage = buttonGO.AddComponent<Image>();
            buttonImage.color = new Color(0.1f, 0.3f, 0.5f, 0.8f); // Cyberpunk blue

            // Add Button component
            Button button = buttonGO.AddComponent<Button>();

            // Configure button colors
            ColorBlock colors = button.colors;
            colors.normalColor = new Color(0.1f, 0.3f, 0.5f, 0.8f);
            colors.highlightedColor = new Color(0.2f, 0.5f, 0.8f, 1f);
            colors.pressedColor = new Color(0.05f, 0.2f, 0.4f, 1f);
            colors.selectedColor = new Color(0.15f, 0.4f, 0.6f, 1f);
            button.colors = colors;

            // Configure RectTransform
            RectTransform rectTransform = buttonGO.GetComponent<RectTransform>();
            rectTransform.sizeDelta = new Vector2(200, 50);
            rectTransform.anchoredPosition = position;

            // Create text child with TextMeshPro
            GameObject textGO = new GameObject("Text (TMP)");
            textGO.transform.SetParent(buttonGO.transform, false);

            // Add TextMeshPro component
            TextMeshProUGUI textMesh = textGO.AddComponent<TextMeshProUGUI>();
            textMesh.text = text;
            textMesh.fontSize = 18;
            textMesh.color = Color.cyan;
            textMesh.alignment = TextAlignmentOptions.Center;
            textMesh.fontStyle = FontStyles.Bold;

            // Load and assign a cyberpunk font
            TMP_FontAsset cyberpunkFont = LoadCyberpunkFont();
            if (cyberpunkFont != null)
            {
                textMesh.font = cyberpunkFont;
            }
            else
            {
                // Fallback to default TMP font
                textMesh.font = Resources.Load<TMP_FontAsset>("Fonts & Materials/LiberationSans SDF");
            }

            // Configure text RectTransform
            RectTransform textRect = textGO.GetComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.sizeDelta = Vector2.zero;
            textRect.anchoredPosition = Vector2.zero;

            return buttonGO;
        }

        /// <summary>
        /// Loads a cyberpunk-style font for the UI. Tries multiple font options in order of preference.
        /// </summary>
        /// <returns>A TMP_FontAsset or null if none found</returns>
        private static TMP_FontAsset LoadCyberpunkFont()
        {
            // Try to load cyberpunk fonts in order of preference
            string[] fontPaths = {
                "Fonts/WipEout-Fonts-master/WO3 SDF",           // WipEout style - very cyberpunk
                "Fonts/WipEout-Fonts-master/Fusion SDF",        // Fusion style - clean cyberpunk
                "Fonts/square_sans_serif_7 SDF",                // Square sans serif - tech look
                "Fonts/GlacialIndifference-Regular SDF",        // Clean modern font
                "Fonts & Materials/LiberationSans SDF"          // Default fallback
            };

            foreach (string fontPath in fontPaths)
            {
                TMP_FontAsset font = Resources.Load<TMP_FontAsset>(fontPath);
                if (font != null)
                {
                    Debug.Log($"MenUISetupHelper: Loaded font '{font.name}' from '{fontPath}'");
                    return font;
                }
            }

            Debug.LogWarning("MenUISetupHelper: No cyberpunk fonts found, will use default TMP font");
            return null;
        }

        private static void ApplyCyberpunkStyling(GameObject panel, GameObject resumeButton, GameObject settingsButton, GameObject exitButton)
        {
            // Add subtle glow effect to panel
            Outline panelOutline = panel.AddComponent<Outline>();
            panelOutline.effectColor = new Color(0, 1, 1, 0.5f); // Cyan glow
            panelOutline.effectDistance = new Vector2(2, 2);

            // Add glow effects to buttons
            AddButtonGlow(resumeButton, Color.green);
            AddButtonGlow(settingsButton, Color.yellow);
            AddButtonGlow(exitButton, Color.red);
        }

        private static void AddButtonGlow(GameObject button, Color glowColor)
        {
            Outline outline = button.AddComponent<Outline>();
            outline.effectColor = new Color(glowColor.r, glowColor.g, glowColor.b, 0.7f);
            outline.effectDistance = new Vector2(1, 1);

            Shadow shadow = button.AddComponent<Shadow>();
            shadow.effectColor = new Color(glowColor.r, glowColor.g, glowColor.b, 0.3f);
            shadow.effectDistance = new Vector2(2, -2);
        }

        /// <summary>
        /// Manual setup instructions for creating the pause menu in the Unity Editor
        /// </summary>
        [System.Serializable]
        public class SetupInstructions
        {
            [TextArea(10, 20)]
            public string instructions = @"
MANUAL SETUP INSTRUCTIONS FOR MENUI PAUSE MENU:

1. CANVAS SETUP:
   - Open your main scene (e.g., Ouroboros - Base.unity)
   - Find or create a Canvas with these settings:
     * Render Mode: Screen Space - Overlay
     * Sort Order: 100 (above other UI)

2. CREATE MENUI SYSTEM:
   - Right-click on Canvas → Create Empty
   - Name it 'MenUI System'
   - Add Component → MenUISystem script

3. CREATE OVERLAY PANEL:
   - Right-click on 'MenUI System' → UI → Panel
   - Name it 'OverlayPanel'
   - Set RectTransform to stretch full screen (anchor min/max: 0,0 to 1,1)
   - Set Image color to black with transparency (R:0, G:0, B:0, A:0.7)
   - This creates the dark overlay behind the pause menu

4. CREATE PAUSE MENU PANEL:
   - Right-click on 'MenUI System' → UI → Panel
   - Name it 'PauseMenuPanel'
   - Set RectTransform to center of screen with appropriate size
   - Set Image color to dark semi-transparent (R:0.05, G:0.05, B:0.1, A:0.9)

5. CREATE BUTTONS:
   - Right-click on 'PauseMenuPanel' → UI → Button - TextMeshPro
   - Create 3 buttons named: 'ResumeButton', 'SettingsButton', 'ExitButton'
   - Set button texts to: 'RESUME', 'SETTINGS', 'EXIT'
   - Position vertically centered with spacing

6. CONFIGURE TEXTMESHPRO FONTS:
   - Select each button's Text (TMP) child object
   - In Font Asset field, assign a cyberpunk font:
     * Recommended: WO3 SDF (WipEout style)
     * Alternative: Fusion SDF or square_sans_serif_7 SDF
   - Set Font Size: 18-24
   - Set Font Style: Bold

7. STYLING (CYBERPUNK THEME):
   - Button colors: Normal (R:0.1, G:0.3, B:0.5, A:0.8)
   - Text color: Cyan (#00FFFF)
   - Add Outline components for glow effects

8. CONFIGURE MENUI SYSTEM:
   - Drag OverlayPanel to the Overlay Panel field
   - Drag PauseMenuPanel to the Pause Menu Panel field
   - Drag buttons to their respective fields
   - Canvas should auto-detect
   - Adjust Overlay Color as desired

9. GAMEMANAGER INTEGRATION:
   - Find GameManager in scene
   - Drag MenUI System to the 'Pause Menu Manager' field

The system will automatically handle input and time management!
";
        }
    }

#if UNITY_EDITOR
    /// <summary>
    /// Editor utility for creating the pause menu UI
    /// </summary>
    public class MenUISetupUtility
    {
        [UnityEditor.MenuItem("Stylo/MenUI/Create Pause Menu UI")]
        public static void CreatePauseMenuUI()
        {
            Canvas canvas = Object.FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                Debug.LogError("No Canvas found in scene! Create a Canvas first.");
                return;
            }

            MenUISetupHelper.CreatePauseMenuUI(canvas);
        }

        [UnityEditor.MenuItem("Stylo/MenUI/Show Setup Instructions")]
        public static void ShowSetupInstructions()
        {
            var instructions = new MenUISetupHelper.SetupInstructions();
            Debug.Log(instructions.instructions);
        }
    }
#endif
}
